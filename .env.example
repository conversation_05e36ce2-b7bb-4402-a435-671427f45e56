# Development Environment Configuration

# Telegram Bot
BOT_TOKEN=your_bot_token_here

# Database
POSTGRES_DB=telebot
POSTGRES_USER=telebot_user
POSTGRES_PASSWORD=dev_password_123

# Redis
REDIS_ENABLED=true
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Webhook (отключен для разработки)
WEBHOOK_MODE=false
WEBHOOK_HOST=http://localhost:8000
WEBHOOK_PATH=/webhook
WEB_SERVER_HOST=0.0.0.0
WEB_SERVER_PORT=8000

# Development
ENVIRONMENT=development
