"""
Обновление parent_test_id для контрольных тестов месяца
"""
import asyncio
from database import MonthTestRepository
from database.database import get_db_session


async def update_control_tests_parent_id():
    print('=== ОБНОВЛЕНИЕ PARENT_TEST_ID ДЛЯ КОНТРОЛЬНЫХ ТЕСТОВ ===')
    
    # Получаем все тесты
    all_tests = await MonthTestRepository.get_all()
    entry_tests = [t for t in all_tests if t.test_type == 'entry']
    control_tests = [t for t in all_tests if t.test_type == 'control']
    
    # Соответствие между входными и контрольными тестами
    test_mappings = [
        ('Контрольный тест по алгебре', 'Контрольный тест по алгебре (Контроль)'),
        ('Геометрия и фигуры', 'Геометрия и фигуры (Контроль)'),
        ('Основы программирования', 'Основы программирования (Контроль)')
    ]
    
    async with get_db_session() as session:
        for entry_name, control_name in test_mappings:
            # Находим входной тест
            entry_test = next((t for t in entry_tests if t.name == entry_name), None)
            if not entry_test:
                print(f'❌ Входной тест "{entry_name}" не найден')
                continue
            
            # Находим контрольный тест
            control_test = next((t for t in control_tests if t.name == control_name), None)
            if not control_test:
                print(f'❌ Контрольный тест "{control_name}" не найден')
                continue
            
            # Обновляем parent_test_id
            control_test.parent_test_id = entry_test.id
            session.add(control_test)
            print(f'✅ Обновлен: {control_name} -> parent_test_id = {entry_test.id}')
        
        await session.commit()
    
    print('\n📊 Обновление завершено!')


if __name__ == "__main__":
    asyncio.run(update_control_tests_parent_id())
